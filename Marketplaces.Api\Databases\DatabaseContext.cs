using System.Linq.Expressions;
using Marketplaces.Api.Controllers;
using Marketplaces.Api.Models;
using Marketplaces.Api.Models.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace Marketplaces.Api.Databases;

public class DatabaseContext(DbContextOptions<DatabaseContext> options) : IdentityDbContext<ApplicationUser>(options)
{
    public DbSet<Shop> Shops { get; set; } = null!;
    public DbSet<Nomenclature> Nomenclatures { get; set; } = null!;
    public DbSet<ShopUser> ShopUsers { get; set; } = null!;
    public DbSet<PriceSnapshot> Snapshots { get; set; } = null!;
    public DbSet<Payment> Payments { get; set; } = null!;


    public IQueryable<Nomenclature> FullNomenclatures
    {
        get => Nomenclatures
            .Include(n => n.Ozon)
            .Include(n => n.Wildberries)
            .Include(n => n.Yandex);
    }


    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);
        builder.Entity<Shop>(e =>
        {
            e.ToTable(nameof(Shop));
            e.<PERSON><Nomenclature>().WithOne().HasForeignKey(n => n.ShopId).OnDelete(DeleteBehavior.Cascade);
            e.HasOne<PriceSnapshot>().WithOne().HasForeignKey<PriceSnapshot>(n => n.ShopId).OnDelete(DeleteBehavior.Cascade);

        });
        builder.Entity<Nomenclature>(e =>
        {
            e.ToTable(nameof(Nomenclature));
        });
        builder.Entity<ShopUser>(e =>
        {
            e.ToTable(nameof(ShopUser));
            e.HasKey(k => new { k.UserId, k.ShopId });
            e.HasOne(su => su.Shop).WithMany(s => s.ShopUsers).HasForeignKey(su => su.ShopId);
            e.HasOne(su => su.User).WithMany(u => u.ShopUsers).HasForeignKey(su => su.UserId);
        });
        builder.Entity<YandexNomenclature>(e =>
        {
            e.ToTable(nameof(YandexNomenclature));
            e.HasKey(k => k.InternalId);
        });
        builder.Entity<OzonNomenclature>(e =>
        {
            e.ToTable(nameof(OzonNomenclature));
            e.HasKey(k => k.InternalId);

        });
        builder.Entity<WildberriesNomenclature>(e =>
        {
            e.ToTable(nameof(WildberriesNomenclature));
            e.HasKey(k => k.InternalId);
        });
        builder.Entity<PriceSnapshot>(e =>
        {
            e.ToTable(nameof(PriceSnapshot));
            e.HasKey(k => k.ShopId);
        });
        builder.Entity<Payment>(e =>
        {
            e.ToTable(nameof(Payment));
        });
        builder.Entity<Partner>(e =>
        {
            e.ToTable(nameof(Partner));
            e.HasIndex(i => i.PromoCode).IsUnique();
        });
        builder.Entity<CurrentPrice>(e =>
        {
            e.ToTable(nameof(CurrentPrice));
        });
    }
}
