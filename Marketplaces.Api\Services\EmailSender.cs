using System.Net;
using System.Net.Mail;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.Extensions.Options;

namespace Marketplaces.Api.Services;

public class EmailSender(IOptions<AppSettings> options) : IEmailSender
{
    private readonly AppSettings _appSettings = options.Value;

    public async Task SendEmailAsync(string email, string subject, string htmlMessage)
    {
        var smtpClient = new SmtpClient(_appSettings.SmtpUrl) // Укажите ваш SMTP сервер
        {
            Port = 2525,
            // Credentials = CredentialCache.DefaultNetworkCredentials,
            Credentials = new NetworkCredential(_appSettings.DefaultEmail, _appSettings.EmailPassword),
            EnableSsl = false,
        };

        var mailMessage = new MailMessage
        {
            From = new MailAddress(_appSettings.DefaultEmail),
            Subject = subject,
            Body = htmlMessage,
            IsBodyHtml = true
        };

        mailMessage.To.Add(email);

        await smtpClient.SendMailAsync(mailMessage);
    }
}