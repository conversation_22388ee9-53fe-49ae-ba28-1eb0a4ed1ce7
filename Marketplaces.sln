﻿
Microsoft Visual Studio Solution File, Format Version 12.00
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Marketplaces.Api", "Marketplaces.Api\Marketplaces.Api.csproj", "{C29FF256-29B2-437C-BBA1-B7AC026112C0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Marketplaces.Tests", "Marketplaces.Tests\Marketplaces.Tests.csproj", "{A1B2C3D4-E5F6-4321-8765-0123456789AB}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C29FF256-29B2-437C-BBA1-B7AC026112C0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C29FF256-29B2-437C-BBA1-B7AC026112C0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C29FF256-29B2-437C-BBA1-B7AC026112C0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C29FF256-29B2-437C-BBA1-B7AC026112C0}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-4321-8765-0123456789AB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-4321-8765-0123456789AB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-4321-8765-0123456789AB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-4321-8765-0123456789AB}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
EndGlobal
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{1B191BBC-CB29-4278-A185-073D06416116}"
	ProjectSection(SolutionItems) = preProject
		.gitignore = .gitignore
		Dockerfile = Dockerfile
		publish_production.ymal = .github/workflows/publish_production.yml
		publish_testing.ymal = .github/workflows/publish_testing.yml
	EndProjectSection
EndProject