using System.Security.Claims;
using Marketplaces.Api.Models.Identity;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Options;

namespace Marketplaces.Api.Authorization;

public class CustomClaimsPrincipalFactory(
    UserManager<ApplicationUser> userManager,
    IOptions<IdentityOptions> optionsAccessor) : UserClaimsPrincipalFactory<ApplicationUser>(user<PERSON><PERSON><PERSON>, optionsAccessor)
{
    protected override async Task<ClaimsIdentity> GenerateClaimsAsync(ApplicationUser user)
    {
        var identity = await base.GenerateClaimsAsync(user);

        // Проверяем и удаляем дубликаты
        var existingClaim = identity.FindFirst(ClaimTypes.NameIdentifier);
        if (existingClaim != null)
        {
            identity.RemoveClaim(existingClaim);
        }

        identity.AddClaim(new Claim(ClaimTypes.NameIdentifier, user.Id));

        return identity;
    }
}