using Marketplaces.Api.Databases;
using Marketplaces.Api.Extensions;
using Marketplaces.Api.Models;
using Marketplaces.Api.Responses;

namespace Marketplaces.Tests;

public class ProfitCalculationTests
{
    [Fact]
    public void CalculateProfitFields_WithWildberriesData_ShouldCalculateCorrectly()
    {
        var shop = new Shop("Test Shop", "user1")
        {
            BankTax = 7m,
            WildberriesTax = 35m,
            WildberriesMode = TaxMode.Manual
        };

        var wbNomenclature = new WildberriesNomenclature(1, "Test Product", "TEST001", "SKU001", "M", []);
        wbNomenclature.UpdatePrice(650m);
        wbNomenclature.UpdateDiscount(79m);

        var nomenclature = new Nomenclature(shop.Id)
        {
            PurchasePrice = 44.2m
        };
        nomenclature.SetWbNomenclature(wbNomenclature);

        var priceDto = new PriceDto
        {
            Id = 1,
            PurchasePrice = 44.2m,
            Wildberries = new NestedPriceDto
            {
                BasePrice = 650m,
                DiscountPercent = 79m
            }
        };

        // Act
        priceDto.CalculateProfitFields(nomenclature, shop);

        // Assert
        var wb = priceDto.Wildberries;

        Assert.Equal(136.5m, wb.SalePrice);
        Assert.Equal(9.56m, wb.BankCommissionAmount);
        Assert.Equal(47.78m, wb.MarketplaceCommissionAmount);
        Assert.Equal(79.17m, wb.Revenue);
        Assert.Equal(34.97m, wb.Profit);
        Assert.Equal(79.12m, wb.ProfitPercentage.Value, 1); // 1 decimal place precision
    }

    [Fact]
    public void CalculateProfitFields_WithoutDiscount_ShouldCalculateCorrectly()
    {
        // Arrange
        var shop = new Shop("Test Shop", "user1")
        {
            BankTax = 7m,
            OzonTax = 35m
        };

        var ozonNomenclature = new OzonNomenclature(1, "Test Product", "TEST001", "SKU001", null, null, null);
        ozonNomenclature.SetPrice(200m, 200m, 200m, 180m, 220m);

        var nomenclature = new Nomenclature(shop.Id)
        {
            PurchasePrice = 100m
        };
        nomenclature.SetOzonNomenclature(ozonNomenclature);

        var priceDto = new PriceDto
        {
            Id = 1,
            PurchasePrice = 100m,
            Ozon = new NestedPriceDto
            {
                BasePrice = 200m,
                DiscountPercent = null // Без скидки
            }
        };

        // Act
        priceDto.CalculateProfitFields(nomenclature, shop);

        // Assert
        var ozon = priceDto.Ozon;

        Assert.Equal(200m, ozon.SalePrice);
        Assert.Equal(14m, ozon.BankCommissionAmount);
        Assert.Equal(70m, ozon.MarketplaceCommissionAmount);
        Assert.Equal(116m, ozon.Revenue);
        Assert.Equal(16m, ozon.Profit);
        Assert.Equal(16m, ozon.ProfitPercentage);
    }

    [Fact]
    public void CalculateProfitFields_ShouldSelectBestMarketplace()
    {
        // Arrange
        var shop = new Shop("Test Shop", "user1")
        {
            BankTax = 7m,
            OzonTax = 30m,
            WildberriesTax = 35m
        };

        var ozonNomenclature = new OzonNomenclature(1, "Test Product", "TEST001", "SKU001", null, null, null);
        ozonNomenclature.SetPrice(150m, 150m, 150m, 135m, 165m);

        var wbNomenclature = new WildberriesNomenclature(2, "Test Product", "TEST002", "SKU002", "M", []);
        wbNomenclature.UpdatePrice(200m);
        wbNomenclature.UpdateDiscount(50m);

        var nomenclature = new Nomenclature(shop.Id)
        {
            PurchasePrice = 50m
        };
        nomenclature.SetOzonNomenclature(ozonNomenclature);
        nomenclature.SetWbNomenclature(wbNomenclature);

        var priceDto = new PriceDto
        {
            Id = 1,
            PurchasePrice = 50m,
            Ozon = new NestedPriceDto
            {
                BasePrice = 150m,
                DiscountPercent = null
            },
            Wildberries = new NestedPriceDto
            {
                BasePrice = 200m,
                DiscountPercent = 50m // 50% скидка = 100 рублей продажная цена
            }
        };

        // Act
        priceDto.CalculateProfitFields(nomenclature, shop);

        // Assert
        Assert.NotNull(priceDto.Ozon.Profit);
        Assert.NotNull(priceDto.Wildberries.Profit);
        Assert.True(priceDto.Ozon.Profit > priceDto.Wildberries.Profit);
    }

    [Fact]
    public void CalculateProfitFields_WithEstimatedTax_ShouldUseEstimatedCommission()
    {
        // Arrange
        var shop = new Shop("Test Shop", "user1")
        {
            BankTax = 7m,
            WildberriesTax = 35m,
            WildberriesEstimatedTax = 25m,
            WildberriesMode = TaxMode.Estimated
        };

        var wbNomenclature = new WildberriesNomenclature(1, "Test Product", "TEST001", "SKU001", "M", []);
        wbNomenclature.UpdatePrice(100m);
        wbNomenclature.UpdateDiscount(0m);

        var nomenclature = new Nomenclature(shop.Id)
        {
            PurchasePrice = 50m
        };
        nomenclature.SetWbNomenclature(wbNomenclature);

        var priceDto = new PriceDto
        {
            Id = 1,
            PurchasePrice = 50m,
            Wildberries = new NestedPriceDto
            {
                BasePrice = 100m,
                DiscountPercent = 0m
            }
        };

        // Act
        priceDto.CalculateProfitFields(nomenclature, shop);

        // Assert
        var wb = priceDto.Wildberries;

        Assert.Equal(100m, wb.SalePrice);
        Assert.Equal(7m, wb.BankCommissionAmount);
        Assert.Equal(25m, wb.MarketplaceCommissionAmount);
        Assert.Equal(68m, wb.Revenue);
        Assert.Equal(18m, wb.Profit);
        Assert.Equal(36m, wb.ProfitPercentage);
    }

    [Fact]
    public void CalculateProfitFields_WithEstimatedTaxNull_ShouldFallbackToManual()
    {
        // Arrange
        var shop = new Shop("Test Shop", "user1")
        {
            BankTax = 7m,
            WildberriesTax = 35m,
            WildberriesEstimatedTax = null,
            WildberriesMode = TaxMode.Estimated
        };

        var wbNomenclature = new WildberriesNomenclature(1, "Test Product", "TEST001", "SKU001", "M", []);
        wbNomenclature.UpdatePrice(100m);
        wbNomenclature.UpdateDiscount(0m);

        var nomenclature = new Nomenclature(shop.Id)
        {
            PurchasePrice = 50m
        };
        nomenclature.SetWbNomenclature(wbNomenclature);

        var priceDto = new PriceDto
        {
            Id = 1,
            PurchasePrice = 50m,
            Wildberries = new NestedPriceDto
            {
                BasePrice = 100m,
                DiscountPercent = 0m
            }
        };

        // Act
        priceDto.CalculateProfitFields(nomenclature, shop);

        // Assert
        var wb = priceDto.Wildberries;
        Assert.Equal(35m, wb.MarketplaceCommissionAmount);
        Assert.Equal(58m, wb.Revenue);
    }
}
