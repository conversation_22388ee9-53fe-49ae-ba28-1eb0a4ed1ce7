using System.Text.Json;
using AutoMapper;
using Marketplaces.Api.Authorization;
using Marketplaces.Api.Databases;
using Marketplaces.Api.Helpers;
using Marketplaces.Api.Requests;
using Marketplaces.Api.Requests.Examples;
using Marketplaces.Api.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Filters;

namespace Marketplaces.Api.Controllers;

[ApiController]
[Route("shops/current/[controller]")]
[Authorize]
public class InfoController(DatabaseContext context, IMapper mapper) : ShopAbstractController(context)
{
    [HttpGet]
    public async Task<IActionResult> GetInfo()
    {
        var shop = await GetShop();
        var dto = mapper.Map<InfoShopDto>(shop);
        return Ok(dto);
    }

    [HttpPatch]
    [Authorize(Policy = Policies.ActiveSubscription)]
    [SwaggerRequestExample(typeof(UpdateInfoBody), typeof(UpdateInfoBodyExample))] // Указываем пример входной модели
    public async Task<IActionResult> UpdateInfo(JsonDocument json)
    {
        var shop = await GetShop();
        var updateObject = new UpdateInfoBody()
        {
            BankTax = shop.BankTax,
            OzonTax = shop.OzonTax,
            OzonMode = shop.OzonMode,
            WildberriesTax = shop.WildberriesTax,
            WildberriesMode = shop.WildberriesMode,
            YandexTax = shop.YandexTax,
            MinimumProfitPercentage = shop.MinimumProfitPercentage,
            MinimumRevenue = shop.MinimumRevenue,
            IsTrackingEnabled = shop.IsTrackingEnabled
        };

        updateObject.Patch(json.RootElement);
        shop.UpdateInfo(updateObject.BankTax,
            updateObject.OzonTax,
            updateObject.OzonMode,
            updateObject.WildberriesTax,
            updateObject.WildberriesMode,
            updateObject.YandexTax,
            updateObject.MinimumProfitPercentage,
            updateObject.MinimumRevenue,
            updateObject.IsTrackingEnabled);
        await Context.SaveChangesAsync();
        var dto = mapper.Map<InfoShopDto>(shop);
        return Ok(dto);
    }
}