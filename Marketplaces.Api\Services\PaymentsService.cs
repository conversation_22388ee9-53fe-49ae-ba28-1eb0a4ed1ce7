using Marketplaces.Api.Clients;
using Marketplaces.Api.Controllers;
using Marketplaces.Api.Databases;
using Marketplaces.Api.Exceptions;
using Marketplaces.Api.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using static System.Data.IsolationLevel;

namespace Marketplaces.Api.Services;

public class PaymentsService(YookassaClient yookassaClient, DatabaseContext databaseContext, IMemoryCache memoryCache)
{
    public async Task ProcessPayments(List<Guid> externalIds)
    {
        foreach (var externalId in externalIds)
        {
            var paymentDto = await yookassaClient.GetPayment(externalId);
            switch (paymentDto?.Status)
            {
                case "succeeded":
                    await CreateOrUpdateSubscription(externalId);
                    break;
                case "canceled":
                    await CancelPayment(externalId);
                    break;
                case null:
                    break;
            }
        }
    }

    private async Task CancelPayment(Guid externalId)
    {
        var payment = await databaseContext.Payments
            .FirstOrDefaultAsync(p => p.ExternalId == externalId);

        payment?.Cancel();
        await databaseContext.SaveChangesAsync();
    }

    private async Task CreateOrUpdateSubscription(Guid externalId)
    {
        await using var transaction = await databaseContext.Database.BeginTransactionAsync(Serializable);
        var aggregation = await databaseContext.Shops
            .Include(s => s.Subscriptions)
            .Join(databaseContext.Set<Payment>(),
                s => s.Id,
                p => p.ShopId,
                (s, p) => new { Shop = s, Payment = p })
            .FirstOrDefaultAsync(s => s.Payment.ExternalId == externalId);

        if (aggregation is null)
        {
            throw new ApiException("Shop not found during attempt to create subscription");
        }

        var shop = aggregation.Shop;
        var payment = aggregation.Payment;

        payment.Confirm();
        shop.CrateOrProlongSubscription(DateTimeOffset.Now.AddMonths(1));

        databaseContext.Update(shop);
        databaseContext.Update(payment);

        await databaseContext.SaveChangesAsync();
        await transaction.CommitAsync();

        var users = await databaseContext.ShopUsers
            .Include(su => su.User)
            .Where(su => su.ShopId == shop.Id)
            .Select(su => su.User)
            .ToListAsync();

        users.ForEach(u => memoryCache.Remove($"userId#{u.Id}"));
    }
}