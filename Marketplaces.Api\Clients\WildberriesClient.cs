using System.Diagnostics;
using Marketplaces.Api.Dtos.Wildberries;
using Marketplaces.Api.Exceptions;
using Marketplaces.Api.Models;
using Marketplaces.Api.Requests;
using Marketplaces.Api.Requests.Wildberries;
using Marketplaces.Api.Responses.Wildberries;
using Microsoft.Extensions.Caching.Memory;
using RestSharp;
using RestSharp.Authenticators;

namespace Marketplaces.Api.Clients;

public class WildberriesClient(ILogger<WildberriesClient> logger, IMemoryCache memoryCache)
{
    private const string GetNomenclaturesUrl = "https://content-api.wildberries.ru/content/v2/get/cards/list";
    private const string GetWarehousesUrl = "https://marketplace-api.wildberries.ru/api/v3/warehouses";
    private const string StocksUrl = "https://marketplace-api.wildberries.ru/api/v3/stocks/{WarehouseId}";
    private const string GetPriceUrl = "https://discounts-prices-api.wildberries.ru/api/v2/list/goods/filter";
    private const string UpdatePriceUrl = "https://discounts-prices-api.wildberries.ru/api/v2/upload/task";
    private const string GetReportUrl = "https://statistics-api.wildberries.ru/api/v5/supplier/reportDetailByPeriod";
    private const string GetOrdersUrl = "https://marketplace-api.wildberries.ru/api/v3/orders";

    private const int Limit = 100;

    public async Task<List<WbWarehouseDto>> GetWarehouses(IAuthenticationData? authenticationData)
    {
        if (authenticationData is not WbAuthenticationData wbAuthenticationData)
        {
            throw new ApiException("Not suitable AuthenticationData");
        }

        var options = new RestClientOptions()
        {
            Authenticator = new JwtAuthenticator(wbAuthenticationData.Token)
        };

        var client = new RestClient(options);
        var request = new RestRequest(GetWarehousesUrl);


        var response = await client.ExecuteAsync<List<WbWarehouseDto>>(request);
        var data = HandleResponse(response);
        return data;
    }


    public async Task<List<WbReportItemtDto>> GetReportDtos(IAuthenticationData? authenticationData,
        DateTime dateFrom, DateTime dateTo)
    {
        if (authenticationData is not WbAuthenticationData wbAuthenticationData)
        {
            throw new ApiException("Not suitable AuthenticationData");
        }

        var options = new RestClientOptions()
        {
            Authenticator = new JwtAuthenticator(wbAuthenticationData.Token)
        };

        var client = new RestClient(options);
        var request = new RestRequest(GetReportUrl);
        request.AddQueryParameter("dateFrom", dateFrom.ToString("s"));
        request.AddQueryParameter("dateTo", dateTo.ToString("s"));

        var response = await client.ExecuteAsync<List<WbReportItemtDto>>(request);
        var data = HandleResponse(response);
        return data;
    }

    public async Task<List<WbFbsStockDto>> GetLastFboStocks(IAuthenticationData? authenticationData, int shopId)
    {
        if (authenticationData is not WbAuthenticationData wbAuthenticationData)
        {
            throw new ApiException("Not suitable AuthenticationData");
        }

        var options = new RestClientOptions()
        {
            Authenticator = new JwtAuthenticator(wbAuthenticationData.Token)
        };


        var client = new RestClient(options);
        var request = new RestRequest("https://statistics-api.wildberries.ru/api/v1/supplier/stocks", Method.Get);

        StocksWbCache? cache = null;
        var dateFrom = new DateTime(2021, 1, 1);
        if (memoryCache.TryGetValue(shopId, out cache) && cache != null)
        {
            dateFrom = cache.DateFrom;
        }

        request.AddQueryParameter("dateFrom", dateFrom.ToString("s"));

        var stopwatch = new Stopwatch();
        stopwatch.Start();
        var response = await client.ExecuteAsync<List<WbFbsStockDto>>(request);
        var data = HandleResponse(response);

        stopwatch.Stop();
        if (cache == null)
        {
            cache = new StocksWbCache()
            {
                ShopId = shopId,
                DateFrom = DateTime.Now,
                Stocks = data
            };
        }
        else
        {
            foreach (var dto in data)
            {
                var old = cache.Stocks.Find(c => c.Barcode == dto.Barcode && c.WarehouseName == dto.WarehouseName);
                if (old != null)
                {
                    old.Quantity = dto.Quantity;
                }
                else
                {
                    cache.Stocks.Add(dto);
                }
            }
        }

        memoryCache.Set(shopId, cache);
        return data;
    }

    public async Task<List<WbNomenclatureDto>> GetNomenclatures(IAuthenticationData authenticationData)
    {
        if (authenticationData is not WbAuthenticationData wbAuthenticationData)
        {
            return [];
        }

        var results = new List<WbNomenclatureDto>();
        var options = new RestClientOptions()
        {
            Authenticator = new JwtAuthenticator(wbAuthenticationData.Token)
        };

        var client = new RestClient(options);
        var request = new RestRequest(GetNomenclaturesUrl, Method.Post);

        var requestBody = new GetWbNomenclaturesBody()
        {
            Settings = new InternalSettings()
            {
                Filter = new InternalFilter() { WithPhoto = 1 },
                Cursor = new InternalCursor() { Limit = Limit }
            },
        };

        while (true)
        {
            request.AddBody(requestBody);

            var response = await client.ExecuteAsync<WbCardsDto>(request);
            var data = HandleResponse(response);

            results.AddRange(data.Cards);
            if (data.Cursor.Total < Limit)
            {
                return results;
            }

            requestBody.Settings.Cursor.NmID = data.Cursor.NmID;
            requestBody.Settings.Cursor.UpdatedAt = data.Cursor.UpdatedAt;
        }
    }

    public async Task<List<WbStockDto>> GetStocks(IAuthenticationData authenticationData, int warehouseId,
        List<string> skus)
    {
        if (authenticationData is not WbAuthenticationData wbAuthenticationData)
        {
            throw new ApiException("Not suitable AuthenticationData");
        }

        var results = new List<WbStockDto>();
        var options = new RestClientOptions()
        {
            Authenticator = new JwtAuthenticator(wbAuthenticationData.Token)
        };

        var client = new RestClient(options);

        var skusChunks = skus.Chunk(100);
        foreach (var skusChunk in skusChunks)
        {
            var request = new RestRequest(StocksUrl, Method.Post);
            request.AddUrlSegment("WarehouseId", warehouseId);
            var requestBody = new { skus = skusChunk.ToList() };
            request.AddBody(requestBody);

            var response = await client.ExecuteAsync<WbStockWrapperDto>(request);
            var data = HandleResponse(response);
            results.AddRange(data.Stocks);
        }

        return results;
    }

    public Task UpdateStocks(IAuthenticationData? authenticationData, int warehouseId, string code, string sku,
        int amount)
    {
        return UpdateStocks(authenticationData, warehouseId, [new UpdateStocksItem(code, sku, amount)]);
    }

    public async Task UpdateStocks(IAuthenticationData? authenticationData, int warehouseId,
        List<UpdateStocksItem> items)
    {
        if (items.Count == 0)
        {
            return;
        }

        if (authenticationData is not WbAuthenticationData wbAuthenticationData)
        {
            throw new ApiException("Not suitable AuthenticationData");
        }

        var options = new RestClientOptions()
        {
            Authenticator = new JwtAuthenticator(wbAuthenticationData.Token)
        };

        var client = new RestClient(options);
        var request = new RestRequest(StocksUrl, Method.Put);
        request.AddUrlSegment("WarehouseId", warehouseId);
        var requestBody = new
        {
            Stocks = items.Select(n => new
            {
                Sku = n.Sku,
                Amount = n.FbsAmount
            })
        };

        request.AddBody(requestBody);
        var response = await client.ExecuteAsync(request);
        HandleResponse(response);
    }

    public async Task<List<WbPricesDto>> GetPrices(IAuthenticationData? authenticationData, List<int> ids)
    {
        logger.LogInformation("Получаем цены из WB");
        if (authenticationData is not WbAuthenticationData wbAuthenticationData)
        {
            throw new ApiException("Not suitable AuthenticationData");
        }

        var results = new List<WbPricesDto>();
        var options = new RestClientOptions(GetPriceUrl)
        {
            Authenticator = new JwtAuthenticator(wbAuthenticationData.Token)
        };

        var client = new RestClient(options);
        var offset = 0;

        while (true)
        {
            var request = new RestRequest("");
            request.AddQueryParameter("limit", Limit.ToString());
            request.AddQueryParameter("offset", offset.ToString());
            var response = await client.ExecuteAsync<WbPricesWrapperDto>(request);
            var data = HandleResponse(response);
            results.AddRange(data.Data.ListGoods);
            offset += Limit;
            if (data.Data.ListGoods.Count != Limit)
            {
                break;
            }
        }

        return results;
    }

    public async Task UpdatePrice(IAuthenticationData? authenticationData,
        List<(int Id, decimal Discount, decimal Price)> wbPrices)
    {
        if (wbPrices.Count == 0)
        {
            return;
        }

        if (authenticationData is not WbAuthenticationData wbAuthenticationData)
        {
            throw new ApiException("Not suitable AuthenticationData");
        }

        var options = new RestClientOptions()
        {
            Authenticator = new JwtAuthenticator(wbAuthenticationData.Token)
        };

        var client = new RestClient(options);
        var request = new RestRequest(UpdatePriceUrl, Method.Post);
        var requestBody = new
        {
            Data = wbPrices.Select(p => new
            {
                nmId = p.Id,
                price = (int)p.Price,
                discount = (int)p.Discount
            }
            )
        };

        request.AddBody(requestBody);
        var response = await client.ExecuteAsync(request);
        HandleResponse(response);
    }

    public async Task<List<WbOrderDto>> GetOrders(IAuthenticationData? authenticationData)
    {
        if (authenticationData is not WbAuthenticationData wbAuthenticationData)
        {
            throw new ApiException("Not suitable AuthenticationData");
        }

        var options = new RestClientOptions()
        {
            Authenticator = new JwtAuthenticator(wbAuthenticationData.Token)
        };

        var results = new List<WbOrderDto>();
        var client = new RestClient(options);
        var next = 0L;
        while (true)
        {
            var request = new RestRequest(GetOrdersUrl);

            request.AddQueryParameter("limit", 1000);
            request.AddQueryParameter("next", next);
            request.AddQueryParameter("dateFrom", DateTimeOffset.Now.AddDays(-5).ToUnixTimeSeconds().ToString());
            request.AddQueryParameter("dateTo", DateTimeOffset.Now.ToUnixTimeSeconds().ToString());
            var response = await client.ExecuteAsync<OrdersDto>(request);
            if (!response.IsSuccessful || response.Data == null)
            {
                throw new ApiException("Получить статистику по заказам не удалось");
            }

            results.AddRange(response.Data.Orders);
            next = response.Data.Next;
            if (response.Data.Next == 0)
            {
                break;
            }
        }

        return results;
    }

    private T HandleResponse<T>(RestResponse<T> response)
    {
        if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
        {
            logger.LogError("Unauthorized access - 401");
            throw new UnauthorizedAccessException("Доступ к wildberries закрыт, проверьте токен");
        }

        if (!response.IsSuccessful || response.Data is null)
        {
            logger.LogError(response.ErrorException, "Error: message: {Message}", response.Content);
            throw new ApiException("Произошла ошибка при обращении к wildberries");
        }

        return response.Data;
    }

    private void HandleResponse(RestResponse response)
    {
        if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
        {
            logger.LogError("Unauthorized access - 401");
            throw new BadRequestException("Доступ к wildberries закрыт, проверьте токен");
        }

        if (!response.IsSuccessful)
        {
            logger.LogError(response.ErrorException, "Error: message: {Message}", response.Content);
            throw new ApiException("Произошла ошибка при обращении к wildberries");
        }
    }
}