using System.Text.Json;
using AutoMapper;
using Marketplaces.Api.Authorization;
using Marketplaces.Api.Clients;
using Marketplaces.Api.Databases;
using Marketplaces.Api.Dtos.Internal;
using Marketplaces.Api.Exceptions;
using Marketplaces.Api.Helpers;
using Marketplaces.Api.Models;
using Marketplaces.Api.Requests;
using Marketplaces.Api.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PriceDto = Marketplaces.Api.Responses.PriceDto;

namespace Marketplaces.Api.Controllers;

[ApiController]
[Route("shops/current/[controller]")]
[Authorize(Policy = Policies.ActiveSubscription)]
public class NomenclaturesController(DatabaseContext context, IMapper mapper, WildberriesClient wbClient,
    OzonClient ozonClient, YandexClient yandexClient, ILogger<NomenclaturesController> logger) : ShopAbstractController(context)
{
    [HttpGet]
    public async Task<IActionResult> GetNomenclatures()
    {
        var shop = await GetShop();
        var nomenclatures = await Context.FullNomenclatures.Where(n => n.ShopId == shop.Id)
            .OrderBy(n => n.Name)
            .ToListAsync();

        nomenclatures.LeaveOnlyWithTokens(shop);

        var dtos = mapper.Map<List<NomenclatureDto>>(nomenclatures.SortByWierdRules());
        return Ok(dtos);
    }

    [HttpPost("sync")]
    public async Task<IActionResult> Sync(SyncNomenclatureBody body)
    {
        var shop = await GetShop();

        var nomenclatures = await Context.FullNomenclatures
            .Where(n => n.ShopId == shop.Id)
            .ToListAsync();


        if (body.Marketplaces.Contains(Marketplace.Ozon))
        {
            var ozonNomenclatures = await ozonClient.GetNomenclatures(shop);
            NomenclatureAggregator.UpsertNewNomenclatures(shop.Id, nomenclatures, ozonNomenclatures);
        }

        if (body.Marketplaces.Contains(Marketplace.Wildberries))
        {
            var wbAuthenticationData = shop.GetToken(Marketplace.Wildberries);
            if (wbAuthenticationData != null)
            {
                var wbNomenclatures = await wbClient.GetNomenclatures(wbAuthenticationData);
                NomenclatureAggregator.UpsertNewNomenclatures(shop.Id, nomenclatures, wbNomenclatures);
            }
        }

        if (body.Marketplaces.Contains(Marketplace.Yandex))
        {
            var yandexAuthenticationData = shop.GetToken(Marketplace.Yandex);
            if (yandexAuthenticationData != null)
            {
                var yandexNomenclatures = await yandexClient.GetNomenclatures(yandexAuthenticationData);
                NomenclatureAggregator.UpsertNewNomenclatures(shop.Id, nomenclatures, yandexNomenclatures);
            }
        }


        Context.Nomenclatures.UpdateRange(nomenclatures);
        await Context.SaveChangesAsync();
        nomenclatures.LeaveOnlyWithTokens(shop);
        var dtos = mapper.Map<List<NomenclatureDto>>(nomenclatures.SortByWierdRules());
        return Ok(dtos);
    }

    [HttpPut("{nomenclatureId}")]
    public async Task<IActionResult> Merge(int nomenclatureId, MergeNomenclatureBody body)
    {
        var shop = await GetShop();

        var baseNomenclature = await Context.FullNomenclatures
            .Where(n => n.ShopId == shop.Id)
            .FirstOrDefaultAsync(n => n.Id == nomenclatureId);

        var nomenclatureToMerge = await Context.FullNomenclatures
            .Where(n => n.ShopId == shop.Id)
            .FirstOrDefaultAsync(n => n.Id == body.Id);

        if (nomenclatureToMerge is null || baseNomenclature is null)
        {
            throw new NotFoundException();
        }

        baseNomenclature.Merge(nomenclatureToMerge);
        await Context.SaveChangesAsync();

        if (!nomenclatureToMerge.HasAnyNomenclatures())
        {
            Context.Remove(nomenclatureToMerge);
            await Context.SaveChangesAsync();
        }

        var dto = mapper.Map<NomenclatureDto>(baseNomenclature);
        return Ok(dto);
    }

    [HttpPut("{nomenclatureId}/minimum/")]
    public async Task<IActionResult> UpdateMinimums(int nomenclatureId, UpdateMinimumsBody body)
    {
        var shop = await GetShop();

        var nomenclature = await Context.FullNomenclatures
            .Where(n => n.ShopId == shop.Id)
            .FirstOrDefaultAsync(n => n.Id == nomenclatureId);

        if (nomenclature is null)
        {
            throw new NotFoundException();
        }

        var marketplaceNomenclature = nomenclature.GetMarketplaceNomenclature(body.MarketplaceType);
        marketplaceNomenclature?.UpdateMinimum(body.MinimumProfitPercentage, body.MinimumRevenue);
        await Context.SaveChangesAsync();
        var dto = mapper.Map<PriceDto>(nomenclature);
        return Ok(dto);
    }

    [HttpDelete("{nomenclatureId:int}")]
    public async Task<IActionResult> Delete(int nomenclatureId)
    {
        var shop = await GetShop();
        var nomenclature = await Context.FullNomenclatures
            .Where(n => n.ShopId == shop.Id)
            .FirstOrDefaultAsync(n => n.Id == nomenclatureId);

        if (nomenclature is null)
        {
            throw new NotFoundException();
        }

        Context.Remove(nomenclature);
        await Context.SaveChangesAsync();
        return Ok();
    }

    [HttpPost("{nomenclatureId}/split")]
    public async Task<IActionResult> Split(int nomenclatureId, SplitNomenclatureBody body)
    {
        var shop = await GetShop();
        var nomenclature = await Context.FullNomenclatures
            .Where(n => n.ShopId == shop.Id)
            .FirstOrDefaultAsync(n => n.Id == nomenclatureId);

        if (nomenclature is null)
        {
            throw new NotFoundException();
        }

        var newNomenclature = nomenclature.SplitForMarketplace(body.Marketplace);
        await Context.AddAsync(newNomenclature);
        await Context.SaveChangesAsync();

        var dto = mapper.Map<NomenclatureDto>(newNomenclature);
        return Ok(dto);
    }

    [HttpPatch("{nomenclatureId}")]
    public async Task<IActionResult> UpdatePurchasePrice(int nomenclatureId, JsonDocument json)
    {
        var shop = await GetShop();
        var nomenclature = await Context.FullNomenclatures
            .Where(n => n.ShopId == shop.Id)
            .FirstOrDefaultAsync(n => n.Id == nomenclatureId);

        if (nomenclature is null)
        {
            throw new NotFoundException();
        }

        var updateObject = new UpdateNomenclatureBody()
        {
            PurchasePrice = nomenclature.PurchasePrice,
            MinimumRevenue = nomenclature.MinimumRevenue,
            MinimumProfitPercentage = nomenclature.MinimumProfitPercentage
        };

        updateObject.Patch(json.RootElement);
        nomenclature.UpdatePurchasePrice(updateObject.PurchasePrice);
        nomenclature.UpdateMinimums(updateObject.MinimumProfitPercentage, updateObject.MinimumRevenue);
        await Context.SaveChangesAsync();
        return Ok();
    }


    [HttpGet("{nomenclatureId}")]
    public async Task<IActionResult> GetNomenclature(int nomenclatureId)
    {
        var shop = await GetShop();

        var baseNomenclature = await Context.FullNomenclatures
            .FirstOrDefaultAsync(n => n.ShopId == shop.Id && n.Id == nomenclatureId);

        var dto = mapper.Map<FullNomenclatureDto>(baseNomenclature);
        return Ok(dto);
    }

    [HttpGet("{nomenclatureId}/details")]
    public async Task<IActionResult> GetDetails(int nomenclatureId)
    {
        var shop = await GetShop();
        var nomenclature = await Context.FullNomenclatures
            .Where(n => n.ShopId == shop.Id)
            .FirstOrDefaultAsync(n => n.Id == nomenclatureId);

        if (nomenclature is null)
        {
            throw new NotFoundException();
        }

        OzonPriceInfoDto? ozonDetail = null;
        if (nomenclature.Ozon != null)
        {
            try
            {
                var ozonCommissionsDto = (await ozonClient.GetPrices(shop, [nomenclature.Ozon.Code])).FirstOrDefault();
                ozonDetail = mapper.Map<OzonPriceInfoDto>(ozonCommissionsDto);
                ozonDetail.RedemptionAmount = nomenclature.Ozon.RedemptionAmount;
                ozonDetail.ReturnAmount = nomenclature.Ozon.ReturnAmount;
            }
            catch (Exception e)
            {
                logger.LogError(e, "Метод получения цены озона вернул ошибку");
            }
        }

        var wb = mapper.Map<WildberriesPriceInfoDto>(nomenclature.Wildberries);

        return Ok(new
        {
            Ozon = ozonDetail,
            Wildberries = wb
        });
    }
}