using System.Security.Claims;
using Marketplaces.Api.Models.Identity;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;

namespace Marketplaces.Api.Authorization;

public class EmailConfirmedHandler(UserManager<ApplicationUser> userManager) : AuthorizationHandler<EmailConfirmedRequirement>
{
    protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context,
        EmailConfirmedRequirement requirement)
    {
        var userId = context.User.FindFirstValue(ClaimTypes.NameIdentifier);
        if (userId is null)
        {
            return;
        }

        var user = await userManager.FindByIdAsync(userId);
        if (user is { EmailConfirmed: true })
        {
            context.Succeed(requirement);
            return;
        }

        context.Fail(new AuthorizationFailureReason(this, "Для продолжения работы необходимо подтвердить E-mail"));
    }
}