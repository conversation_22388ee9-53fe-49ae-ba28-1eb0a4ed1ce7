using Marketplaces.Api.Databases;
using Marketplaces.Api.Models;
using Marketplaces.Api.Responses;

namespace Marketplaces.Api.Extensions;

public static class NomenclatureExtensions
{
    public static void CalculateProfitFields(this PriceDto priceDto, Nomenclature nomenclature, Shop shop)
    {
        if (priceDto.Ozon != null && nomenclature.Ozon != null)
        {
            var ozonTax = shop.GetMarketplaceTax(Marketplace.Ozon);
            CalculateNestedProfitFields(priceDto.Ozon, nomenclature.PurchasePrice, shop.BankTax, ozonTax);
        }

        if (priceDto.Wildberries != null && nomenclature.Wildberries != null)
        {
            var wbTax = shop.GetMarketplaceTax(Marketplace.Wildberries);
            CalculateNestedProfitFields(priceDto.Wildberries, nomenclature.PurchasePrice, shop.BankTax, wbTax);
        }

        if (priceDto.Yandex != null && nomenclature.Yandex != null)
        {
            var yandexTax = shop.GetMarketplaceTax(Marketplace.Yandex);
            CalculateNestedProfitFields(priceDto.Yandex, nomenclature.PurchasePrice, shop.BankTax, yandexTax);
        }
    }

    private static void CalculateNestedProfitFields(NestedPriceDto nestedDto, decimal? purchasePrice, decimal? bankTax, decimal? marketplaceTax)
    {
        if (nestedDto.BasePrice == null || purchasePrice == null || bankTax == null || marketplaceTax == null)
            return;

        var salePrice = nestedDto.BasePrice.Value;
        if (nestedDto.DiscountPercent.HasValue)
        {
            var discountMultiplier = 1 - (nestedDto.DiscountPercent.Value / 100);
            salePrice = nestedDto.BasePrice.Value * discountMultiplier;
        }

        var bankCommissionAmount = salePrice * (bankTax.Value / 100);

        var marketplaceCommissionAmount = salePrice * (marketplaceTax.Value / 100);

        var revenue = salePrice - bankCommissionAmount - marketplaceCommissionAmount;

        var profit = revenue - purchasePrice.Value;

        var profitPercentage = purchasePrice.Value != 0 ? profit / purchasePrice.Value * 100 : 0;

        nestedDto.SalePrice = Math.Round(salePrice, 2);
        nestedDto.BankCommissionAmount = Math.Round(bankCommissionAmount, 2);
        nestedDto.MarketplaceCommissionAmount = Math.Round(marketplaceCommissionAmount, 2);
        nestedDto.Revenue = Math.Round(revenue, 2);
        nestedDto.Profit = Math.Round(profit, 2);
        nestedDto.ProfitPercentage = Math.Round(profitPercentage, 2);
    }
}
