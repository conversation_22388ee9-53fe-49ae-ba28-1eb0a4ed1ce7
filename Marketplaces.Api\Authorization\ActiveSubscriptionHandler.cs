using System.Security.Claims;
using Marketplaces.Api.Databases;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;

namespace Marketplaces.Api.Authorization;

internal class ActiveSubscriptionHandler(IServiceProvider serviceProvider) : AuthorizationHandler<ActiveSubscriptionRequirement>
{
    protected override Task HandleRequirementAsync(AuthorizationHandlerContext context,
        ActiveSubscriptionRequirement requirement)
    {
        var userId = context.User.FindFirstValue(ClaimTypes.NameIdentifier);
        if (userId is null)
        {
            return Task.CompletedTask;
        }

        var databaseContext = serviceProvider.GetRequiredService<DatabaseContext>();
        var shop = databaseContext.Shops
            .Include(s => s.Subscriptions
                .Where(su => su.EndDate >= DateTime.UtcNow))
            .First(s => s.ShopUsers.Select(su => su.UserId).Contains(userId));

        if (shop.FindSubscription()?.EndDate >= DateTimeOffset.UtcNow)
        {
            context.Succeed(requirement);
            return Task.CompletedTask;
        }

        context.Fail(new AuthorizationFailureReason(this, "Для продолжения работы необходимо оплатить подписку"));
        return Task.CompletedTask;
    }
}